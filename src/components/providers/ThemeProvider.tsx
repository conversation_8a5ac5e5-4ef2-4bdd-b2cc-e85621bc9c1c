import React, { useEffect } from 'react';
import { useColorScheme } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useIsDarkTheme, useThemeActions } from '@/lib/store/selectors';
import { getNavTheme } from '@/lib/theme';
import { ThemeProvider as NavThemeProvider } from '@react-navigation/native';

interface ThemeProviderProps {
  children: React.ReactNode;
}

/**
 * Simplified ThemeProvider that only handles:
 * 1. Theme initialization and system color scheme sync
 * 2. Navigation theme integration
 * 3. Status bar styling
 * 
 * Use useTheme() from @/lib/hooks/useTheme for theme access
 */
export function ThemeProvider({ children }: Readonly<ThemeProviderProps>) {
  const colorScheme = useColorScheme();
  const isDark = useIsDarkTheme();
  const { initializeTheme, setSystemColorScheme } = useThemeActions();

  // Initialize theme system
  useEffect(() => {
    const cleanup = initializeTheme();
    return cleanup;
  }, [initializeTheme]);

  // Sync system color scheme
  useEffect(() => {
    setSystemColorScheme(colorScheme);
  }, [colorScheme, setSystemColorScheme]);

  const navTheme = getNavTheme(isDark);

  return (
    <NavThemeProvider value={navTheme}>
      <StatusBar style={isDark ? 'light' : 'dark'} />
      {children}
    </NavThemeProvider>
  );
}
