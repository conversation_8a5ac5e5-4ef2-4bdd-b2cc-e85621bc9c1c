import React from 'react';
import { View } from 'react-native';
import { useThemeColors } from '@/lib/store/selectors';
import { Text } from '@/components/ui/text';

interface ProgressBarProps {
  readonly currentStep: number;
  readonly totalSteps: number;
  readonly stepTitles: string[];
}

export default function ProgressBar({
  currentStep,
  totalSteps,
  stepTitles,
}: ProgressBarProps) {
  const colors = useThemeColors();

  return (
    <View className="p-5 bg-card border-b border-border">
      <Text className="text-lg font-semibold text-foreground mb-4 text-center">
        Step {currentStep} of {totalSteps}: {stepTitles[currentStep - 1]}
      </Text>

      <View className="mb-5">
        <View className="h-2 bg-muted rounded-sm mb-2 overflow-hidden">
          <View
            className="h-full bg-primary rounded-sm"
            style={{
              width: `${(currentStep / totalSteps) * 100}%`,
            }}
          />
        </View>
        <Text className="text-sm font-medium text-muted-foreground text-center">
          {Math.round((currentStep / totalSteps) * 100)}% Complete
        </Text>
      </View>

      <View className="flex-row justify-between">
        {Array.from({ length: totalSteps }, (_, index) => (
          <View key={index} className="items-center flex-1">
            <View
              className="w-8 h-8 rounded-full border-2 items-center justify-center mb-2"
              style={{
                backgroundColor:
                  index < currentStep ? colors.primary : colors.borderLight,
                borderColor:
                  index === currentStep - 1 ? colors.primary : colors.border,
              }}
            >
              <Text
                className="text-sm font-semibold"
                style={{
                  color:
                    index < currentStep
                      ? colors.textInverse
                      : colors.textSecondary,
                }}
              >
                {index + 1}
              </Text>
            </View>
            <Text
              className="text-xs text-center max-w-[80px]"
              style={{
                color:
                  index === currentStep - 1
                    ? colors.primary
                    : colors.textSecondary,
              }}
            >
              {stepTitles[index]}
            </Text>
          </View>
        ))}
      </View>
    </View>
  );
}
