import React from 'react';
import {
  ScrollView,
  Text,
  StyleSheet,
  View,
  TouchableOpacity,
} from 'react-native';
import { useOnboarding } from '@/lib/contexts/OnboardingContext';
import Dropdown from '@/components/onboarding/Dropdown';
import FormField from '@/components/onboarding/FormField';
import DatePicker from '@/components/onboarding/DatePicker';
import MultiSelect from '@/components/onboarding/MultiSelect';
import {
  MEDICATION_FREQUENCIES,
  TIMING_REQUIREMENTS,
  COMMON_MEDICATIONS,
} from '@/types/onboarding';
import { useTheme } from '@/lib/hooks/useTheme';
import PageTransition from '@/components/ui/PageTransition';
import AnimatedHeader from '@/components/ui/AnimatedHeader';
import StaggeredEntrance from '@/components/ui/StaggeredEntrance';

export default function MedicationsStep() {
  const {
    medications,
    setMedications,
    errors,
    addMedication,
    removeMedication,
  } = useOnboarding();
  const { colors } = useTheme();

  return (
    <PageTransition isVisible={true} direction="right">
      <ScrollView
        style={styles.stepContent}
        showsVerticalScrollIndicator={false}
      >
        <AnimatedHeader
          title="Medication Management"
          subtitle="Please list all medications you're currently taking for your liver condition."
          isVisible={true}
          delay={0}
        />

        <StaggeredEntrance isVisible={true} delay={100} animationType="fadeUp">
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Current Medications
            </Text>
            <TouchableOpacity
              style={[styles.addButton, { backgroundColor: colors.primary }]}
              onPress={addMedication}
              accessibilityLabel="Add medication"
            >
              <Text
                style={[styles.addButtonText, { color: colors.textInverse }]}
              >
                Add Medication
              </Text>
            </TouchableOpacity>
          </View>
        </StaggeredEntrance>

        {medications.medications?.length === 0 && (
          <StaggeredEntrance
            isVisible={true}
            delay={150}
            animationType="fadeUp"
          >
            <View
              style={[styles.emptyState, { backgroundColor: colors.surface }]}
            >
              <Text
                style={[styles.emptyStateText, { color: colors.textSecondary }]}
              >
                No medications added yet. Tap &quot;Add Medication&quot; to get
                started.
              </Text>
            </View>
          </StaggeredEntrance>
        )}

        {medications.medications?.map((medication, index) => (
          <StaggeredEntrance
            key={medication.name}
            isVisible={true}
            delay={200 + index * 50}
            animationType="scale"
          >
            <View
              style={[
                styles.medicationCard,
                { backgroundColor: colors.surface },
              ]}
            >
              <View style={styles.cardHeader}>
                <Text style={[styles.cardTitle, { color: colors.text }]}>
                  Medication #{index + 1}
                </Text>
                <TouchableOpacity
                  onPress={() => removeMedication(index)}
                  style={styles.removeButton}
                  accessibilityLabel={`Remove medication ${index + 1}`}
                >
                  <Text
                    style={[styles.removeButtonText, { color: colors.error }]}
                  >
                    Remove
                  </Text>
                </TouchableOpacity>
              </View>

              <Dropdown
                label="Medication Name"
                value={medication.name}
                options={COMMON_MEDICATIONS.map(med => ({
                  value: med,
                  label: med,
                }))}
                onSelect={value => {
                  const updatedMedications = [
                    ...(medications.medications || []),
                  ];
                  updatedMedications[index] = { ...medication, name: value };
                  setMedications({
                    ...medications,
                    medications: updatedMedications,
                  });
                }}
                required
                error={errors[`medications.${index}.name`]}
              />

              <View style={styles.row}>
                <View style={styles.flex2}>
                  <FormField
                    label="Dosage"
                    value={medication.dosage}
                    onChangeText={text => {
                      const updatedMedications = [
                        ...(medications.medications || []),
                      ];
                      updatedMedications[index] = {
                        ...medication,
                        dosage: text,
                      };
                      setMedications({
                        ...medications,
                        medications: updatedMedications,
                      });
                    }}
                    placeholder="Enter dosage"
                    required
                    keyboardType="numeric"
                    error={errors[`medications.${index}.dosage`]}
                  />
                </View>
                <View style={styles.flex1}>
                  <Dropdown
                    label="Unit"
                    value={medication.unit}
                    options={[
                      { value: 'mg', label: 'mg' },
                      { value: 'g', label: 'g' },
                      { value: 'ml', label: 'ml' },
                      { value: 'tablets', label: 'tablets' },
                      { value: 'capsules', label: 'capsules' },
                    ]}
                    onSelect={value => {
                      const updatedMedications = [
                        ...(medications.medications || []),
                      ];
                      updatedMedications[index] = {
                        ...medication,
                        unit: value,
                      };
                      setMedications({
                        ...medications,
                        medications: updatedMedications,
                      });
                    }}
                    required
                    error={errors[`medications.${index}.unit`]}
                  />
                </View>
              </View>

              <Dropdown
                label="Frequency"
                value={medication.frequency}
                options={MEDICATION_FREQUENCIES.map(freq => ({
                  value: freq,
                  label: freq,
                }))}
                onSelect={value => {
                  const updatedMedications = [
                    ...(medications.medications || []),
                  ];
                  updatedMedications[index] = {
                    ...medication,
                    frequency: value,
                  };
                  setMedications({
                    ...medications,
                    medications: updatedMedications,
                  });
                }}
                required
                error={errors[`medications.${index}.frequency`]}
              />

              <MultiSelect
                label="Timing Requirements"
                values={medication.timingRequirements || []}
                options={TIMING_REQUIREMENTS.map(req => ({
                  value: req,
                  label: req,
                }))}
                onSelectionChange={values => {
                  const updatedMedications = [
                    ...(medications.medications || []),
                  ];
                  updatedMedications[index] = {
                    ...medication,
                    timingRequirements: values,
                  };
                  setMedications({
                    ...medications,
                    medications: updatedMedications,
                  });
                }}
                placeholder="Select timing requirements"
                error={errors[`medications.${index}.timingRequirements`]}
              />

              <DatePicker
                label="Start Date"
                value={
                  medication.startDate ? new Date(medication.startDate) : null
                }
                onChange={date => {
                  const updatedMedications = [
                    ...(medications.medications || []),
                  ];
                  updatedMedications[index] = {
                    ...medication,
                    startDate: date,
                  };
                  setMedications({
                    ...medications,
                    medications: updatedMedications,
                  });
                }}
                required
                error={errors[`medications.${index}.startDate`]}
                maximumDate={new Date()}
              />

              <FormField
                label="Special Instructions"
                value={medication.specialInstructions ?? ''}
                onChangeText={text => {
                  const updatedMedications = [
                    ...(medications.medications || []),
                  ];
                  updatedMedications[index] = {
                    ...medication,
                    specialInstructions: text,
                  };
                  setMedications({
                    ...medications,
                    medications: updatedMedications,
                  });
                }}
                placeholder="Any special instructions or notes"
                multiline
                numberOfLines={3}
                error={errors[`medications.${index}.specialInstructions`]}
              />

              <FormField
                label="Prescribing Doctor"
                value={medication.prescribingDoctor}
                onChangeText={text => {
                  const updatedMedications = [
                    ...(medications.medications || []),
                  ];
                  updatedMedications[index] = {
                    ...medication,
                    prescribingDoctor: text,
                  };
                  setMedications({
                    ...medications,
                    medications: updatedMedications,
                  });
                }}
                placeholder="Enter doctor's name"
                required
                error={errors[`medications.${index}.prescribingDoctor`]}
              />
            </View>
          </StaggeredEntrance>
        ))}
      </ScrollView>
    </PageTransition>
  );
}

const styles = StyleSheet.create({
  stepContent: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  sectionTitle: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 18,
    marginBottom: 16,
    marginTop: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    marginTop: 24,
  },
  addButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  addButtonText: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 14,
  },
  medicationCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  cardTitle: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 16,
  },
  removeButton: {
    padding: 8,
  },
  removeButtonText: {
    fontFamily: 'Inter-Medium',
    fontSize: 14,
  },
  row: {
    flexDirection: 'row',
    gap: 12,
  },
  flex1: {
    flex: 1,
  },
  flex2: {
    flex: 2,
  },
  emptyState: {
    borderRadius: 12,
    padding: 32,
    alignItems: 'center',
    marginBottom: 16,
  },
  emptyStateText: {
    fontFamily: 'Inter-Regular',
    fontSize: 16,
    textAlign: 'center',
  },
});
