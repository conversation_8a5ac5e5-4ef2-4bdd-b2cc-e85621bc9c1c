import React from 'react';
import { ScrollView } from 'react-native';
import { useOnboarding } from '@/lib/contexts/OnboardingContext';
import FormField from '@/components/onboarding/FormField';
import DatePicker from '@/components/onboarding/DatePicker';
import Dropdown from '@/components/onboarding/Dropdown';
import { RELATIONSHIPS } from '@/types/onboarding';
import { Text } from '@/components/ui/text';
import PageTransition from '@/components/ui/PageTransition';
import AnimatedHeader from '@/components/ui/AnimatedHeader';
import StaggeredEntrance from '@/components/ui/StaggeredEntrance';

export default function PersonalInfoStep() {
  const { personalInfo, setPersonalInfo, errors } = useOnboarding();

  const formatPhoneNumber = (text: string) => {
    const cleaned = text.replace(/\D/g, '');
    const match = RegExp(/^(\d{3})(\d{3})(\d{4})$/).exec(cleaned);
    if (match) {
      return `(${match[1]}) ${match[2]}-${match[3]}`;
    }
    return text;
  };

  return (
    <PageTransition isVisible={true} direction="right">
      <ScrollView
        className="flex-1 px-5 pt-5"
        showsVerticalScrollIndicator={false}
      >
        <AnimatedHeader
          title="Personal Information"
          subtitle="Please provide your basic information and emergency contact details."
          isVisible={true}
          delay={0}
        />

        <StaggeredEntrance isVisible={true} delay={100} animationType="fadeUp">
          <FormField
            label="First Name"
            value={personalInfo.firstName ?? ''}
            onChangeText={text =>
              setPersonalInfo({ ...personalInfo, firstName: text })
            }
            placeholder="Enter your first name"
            required
            error={errors['firstName']}
            accessibilityLabel="First name input"
          />
        </StaggeredEntrance>

        <StaggeredEntrance isVisible={true} delay={150} animationType="fadeUp">
          <FormField
            label="Middle Name"
            value={personalInfo.middleName ?? ''}
            onChangeText={text =>
              setPersonalInfo({ ...personalInfo, middleName: text })
            }
            placeholder="Enter your middle name (optional)"
            error={errors['middleName']}
            accessibilityLabel="Middle name input"
          />
        </StaggeredEntrance>

        <StaggeredEntrance isVisible={true} delay={200} animationType="fadeUp">
          <FormField
            label="Last Name"
            value={personalInfo.lastName ?? ''}
            onChangeText={text =>
              setPersonalInfo({ ...personalInfo, lastName: text })
            }
            placeholder="Enter your last name"
            required
            error={errors['lastName']}
            accessibilityLabel="Last name input"
          />
        </StaggeredEntrance>

        <StaggeredEntrance isVisible={true} delay={250} animationType="fadeUp">
          <DatePicker
            label="Date of Birth"
            value={
              personalInfo.dateOfBirth
                ? new Date(personalInfo.dateOfBirth)
                : null
            }
            onChange={date =>
              setPersonalInfo({ ...personalInfo, dateOfBirth: date })
            }
            required
            error={errors['dateOfBirth']}
            maximumDate={new Date()}
            accessibilityLabel="Date of birth picker"
          />
        </StaggeredEntrance>

        <StaggeredEntrance isVisible={true} delay={300} animationType="fadeUp">
          <FormField
            label="Email Address"
            value={personalInfo.email ?? ''}
            onChangeText={text =>
              setPersonalInfo({ ...personalInfo, email: text })
            }
            placeholder="Enter your email address"
            required
            keyboardType="email-address"
            autoCapitalize="none"
            error={errors['email']}
            accessibilityLabel="Email address input"
          />
        </StaggeredEntrance>

        <StaggeredEntrance isVisible={true} delay={350} animationType="fadeUp">
          <FormField
            label="Phone Number"
            value={personalInfo.phone ?? ''}
            onChangeText={text => {
              const formatted = formatPhoneNumber(text);
              setPersonalInfo({ ...personalInfo, phone: formatted });
            }}
            placeholder="(XXX) XXX-XXXX"
            keyboardType="phone-pad"
            error={errors['phone']}
            accessibilityLabel="Phone number input"
          />
        </StaggeredEntrance>

        <StaggeredEntrance isVisible={true} delay={400} animationType="fadeUp">
          <Text className="text-lg font-semibold text-foreground mb-4 mt-6">
            Emergency Contact
          </Text>
        </StaggeredEntrance>

        <StaggeredEntrance isVisible={true} delay={450} animationType="fadeUp">
          <FormField
            label="Full Name"
            value={personalInfo.emergencyContact?.fullName ?? ''}
            onChangeText={text =>
              setPersonalInfo({
                ...personalInfo,
                emergencyContact: {
                  ...personalInfo.emergencyContact!,
                  fullName: text,
                },
              })
            }
            placeholder="Enter emergency contact name"
            required
            error={errors['emergencyContact.fullName']}
            accessibilityLabel="Emergency contact name input"
          />
        </StaggeredEntrance>

        <StaggeredEntrance isVisible={true} delay={500} animationType="fadeUp">
          <Dropdown
            label="Relationship"
            value={personalInfo.emergencyContact?.relationship ?? ''}
            options={RELATIONSHIPS.map(rel => ({ value: rel, label: rel }))}
            onSelect={value =>
              setPersonalInfo({
                ...personalInfo,
                emergencyContact: {
                  ...personalInfo.emergencyContact!,
                  relationship: value,
                },
              })
            }
            required
            error={errors['emergencyContact.relationship']}
            accessibilityLabel="Emergency contact relationship dropdown"
          />
        </StaggeredEntrance>

        <StaggeredEntrance isVisible={true} delay={550} animationType="fadeUp">
          <FormField
            label="Primary Phone"
            value={personalInfo.emergencyContact?.primaryPhone ?? ''}
            onChangeText={text => {
              const formatted = formatPhoneNumber(text);
              setPersonalInfo({
                ...personalInfo,
                emergencyContact: {
                  ...personalInfo.emergencyContact!,
                  primaryPhone: formatted,
                },
              });
            }}
            placeholder="(XXX) XXX-XXXX"
            required
            keyboardType="phone-pad"
            error={errors['emergencyContact.primaryPhone']}
            accessibilityLabel="Emergency contact primary phone input"
          />
        </StaggeredEntrance>

        <StaggeredEntrance isVisible={true} delay={600} animationType="fadeUp">
          <FormField
            label="Secondary Phone"
            value={personalInfo.emergencyContact?.secondaryPhone ?? ''}
            onChangeText={text => {
              const formatted = formatPhoneNumber(text);
              setPersonalInfo({
                ...personalInfo,
                emergencyContact: {
                  ...personalInfo.emergencyContact!,
                  secondaryPhone: formatted,
                },
              });
            }}
            placeholder="(XXX) XXX-XXXX (optional)"
            keyboardType="phone-pad"
            error={errors['emergencyContact.secondaryPhone']}
            accessibilityLabel="Emergency contact secondary phone input"
          />
        </StaggeredEntrance>
      </ScrollView>
    </PageTransition>
  );
}
