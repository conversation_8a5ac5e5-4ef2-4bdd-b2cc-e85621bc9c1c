import React from 'react';
import {
  ScrollView,
  Text,
  StyleSheet,
  View,
  TouchableOpacity,
} from 'react-native';
import { useOnboarding } from '@/lib/contexts/OnboardingContext';
import { useTheme } from '@/lib/hooks/useTheme';
import { CircleCheck as CheckCircle } from 'lucide-react-native';
import PageTransition from '@/components/ui/PageTransition';
import AnimatedHeader from '@/components/ui/AnimatedHeader';
import StaggeredEntrance from '@/components/ui/StaggeredEntrance';

export default function ReviewStep() {
  const {
    personalInfo,
    diseaseHistory,
    medications,
    finalConfirmation,
    setFinalConfirmation,
    errors,
    setCurrentStep,
  } = useOnboarding();
  const { colors } = useTheme();

  return (
    <PageTransition isVisible={true} direction="right">
      <ScrollView
        style={styles.stepContent}
        showsVerticalScrollIndicator={false}
      >
        <AnimatedHeader
          title="Review & Submit"
          subtitle="Please review all your information before submitting. You can edit any section by tapping on it."
          isVisible={true}
          delay={0}
        />

        {/* Personal Information Review */}
        <StaggeredEntrance isVisible={true} delay={100} animationType="fadeUp">
          <TouchableOpacity
            style={[styles.reviewSection, { backgroundColor: colors.surface }]}
            onPress={() => setCurrentStep(1)}
            accessibilityLabel="Edit personal information"
          >
            <Text style={[styles.reviewSectionTitle, { color: colors.text }]}>
              Personal Information
            </Text>
            <Text style={[styles.reviewText, { color: colors.textSecondary }]}>
              {personalInfo.firstName} {personalInfo.middleName}{' '}
              {personalInfo.lastName}
            </Text>
            <Text style={[styles.reviewText, { color: colors.textSecondary }]}>
              Born:{' '}
              {personalInfo.dateOfBirth
                ? new Date(personalInfo.dateOfBirth).toLocaleDateString()
                : 'Not provided'}
            </Text>
            <Text style={[styles.reviewText, { color: colors.textSecondary }]}>
              Email: {personalInfo.email}
            </Text>
            <Text style={[styles.reviewText, { color: colors.textSecondary }]}>
              Phone: {personalInfo.phone ?? 'Not provided'}
            </Text>
            <Text style={[styles.reviewText, { color: colors.textSecondary }]}>
              Emergency Contact: {personalInfo.emergencyContact?.fullName} (
              {personalInfo.emergencyContact?.relationship})
            </Text>
          </TouchableOpacity>
        </StaggeredEntrance>

        {/* Disease History Review */}
        <StaggeredEntrance isVisible={true} delay={150} animationType="fadeUp">
          <TouchableOpacity
            style={[styles.reviewSection, { backgroundColor: colors.surface }]}
            onPress={() => setCurrentStep(2)}
            accessibilityLabel="Edit disease history"
          >
            <Text style={[styles.reviewSectionTitle, { color: colors.text }]}>
              Disease History
            </Text>
            <Text style={[styles.reviewText, { color: colors.textSecondary }]}>
              Primary Diagnosis: {diseaseHistory.primaryDiagnosis}
              {diseaseHistory.primaryDiagnosis === 'Other' &&
                diseaseHistory.otherDiagnosis &&
                ` (${diseaseHistory.otherDiagnosis})`}
            </Text>
            <Text style={[styles.reviewText, { color: colors.textSecondary }]}>
              Diagnosed:{' '}
              {diseaseHistory.diagnosisDate
                ? new Date(diseaseHistory.diagnosisDate).toLocaleDateString()
                : 'Not provided'}
            </Text>
            <Text style={[styles.reviewText, { color: colors.textSecondary }]}>
              Stage: {diseaseHistory.diseaseStage}
            </Text>
            {diseaseHistory.secondaryConditions &&
              diseaseHistory.secondaryConditions.length > 0 && (
                <Text
                  style={[styles.reviewText, { color: colors.textSecondary }]}
                >
                  Secondary Conditions:{' '}
                  {diseaseHistory.secondaryConditions.join(', ')}
                </Text>
              )}
            <Text style={[styles.reviewText, { color: colors.textSecondary }]}>
              Test Results: {diseaseHistory.testResults?.length ?? 0} recorded
            </Text>
          </TouchableOpacity>
        </StaggeredEntrance>

        {/* Medications Review */}
        <StaggeredEntrance isVisible={true} delay={200} animationType="fadeUp">
          <TouchableOpacity
            style={[styles.reviewSection, { backgroundColor: colors.surface }]}
            onPress={() => setCurrentStep(3)}
            accessibilityLabel="Edit medications"
          >
            <Text style={[styles.reviewSectionTitle, { color: colors.text }]}>
              Medications
            </Text>
            {medications.medications && medications.medications.length > 0 ? (
              medications.medications.map((med, index) => (
                <Text
                  key={med.name}
                  style={[styles.reviewText, { color: colors.textSecondary }]}
                >
                  {med.name} - {med.dosage} {med.unit}, {med.frequency}
                </Text>
              ))
            ) : (
              <Text
                style={[styles.reviewText, { color: colors.textSecondary }]}
              >
                No medications recorded
              </Text>
            )}
          </TouchableOpacity>
        </StaggeredEntrance>

        {/* Final Confirmation */}
        <StaggeredEntrance isVisible={true} delay={250} animationType="fadeUp">
          <View
            style={[
              styles.confirmationSection,
              { backgroundColor: colors.surface },
            ]}
          >
            <TouchableOpacity
              style={styles.checkboxContainer}
              onPress={() => setFinalConfirmation(!finalConfirmation)}
              accessibilityLabel="Final confirmation checkbox"
              accessibilityRole="checkbox"
              accessibilityState={{ checked: finalConfirmation }}
            >
              <View
                style={[
                  styles.checkbox,
                  {
                    borderColor: colors.border,
                    backgroundColor: finalConfirmation
                      ? colors.primary
                      : 'transparent',
                  },
                ]}
              >
                {finalConfirmation && (
                  <CheckCircle size={16} color={colors.textInverse} />
                )}
              </View>
              <Text style={[styles.checkboxText, { color: colors.text }]}>
                I confirm that all the information provided is accurate and
                complete. I understand that this information will be used to
                provide personalized health recommendations and will be kept
                confidential according to HIPAA guidelines.
              </Text>
            </TouchableOpacity>
            {errors['general'] && (
              <Text style={[styles.errorText, { color: colors.error }]}>
                {errors['general']}
              </Text>
            )}
          </View>
        </StaggeredEntrance>
      </ScrollView>
    </PageTransition>
  );
}

const styles = StyleSheet.create({
  stepContent: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  reviewSection: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  reviewSectionTitle: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 18,
    marginBottom: 12,
  },
  reviewText: {
    fontFamily: 'Inter-Regular',
    fontSize: 14,
    marginBottom: 4,
  },
  confirmationSection: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 4,
    borderWidth: 2,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
    marginTop: 2,
  },
  checkboxText: {
    fontFamily: 'Inter-Regular',
    fontSize: 14,
    lineHeight: 20,
    flex: 1,
  },
  errorText: {
    fontFamily: 'Inter-Regular',
    fontSize: 14,
    marginTop: 8,
  },
});
