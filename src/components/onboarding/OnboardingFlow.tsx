import React from 'react';
import { View, KeyboardAvoidingView, Platform } from 'react-native';
import { Button } from '@/components/ui/button';
import { Text } from '@/components/ui/text';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  <PERSON>L<PERSON><PERSON>,
  ArrowRight,
  CircleCheck as CheckCircle,
} from 'lucide-react-native';
import { useOnboarding } from '@/lib/contexts/OnboardingContext';
import { useThemeColors } from '@/lib/store/selectors';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import ProgressBar from './ProgressBar';
import PersonalInfoStep from './steps/PersonalInfoStep';
import DiseaseHistoryStep from './steps/DiseaseHistoryStep';
import MedicationsStep from './steps/MedicationsStep';
import ReviewStep from './steps/ReviewStep';

const STEP_TITLES = [
  'Personal Info',
  'Disease History',
  'Medications',
  'Review',
];

export default function OnboardingFlow() {
  const colors = useThemeColors();
  const { currentStep, previousStep, nextStep, isSubmitting } = useOnboarding();

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1:
        return <PersonalInfoStep />;
      case 2:
        return <DiseaseHistoryStep />;
      case 3:
        return <MedicationsStep />;
      case 4:
        return <ReviewStep />;
      default:
        return null;
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-background">
      <KeyboardAvoidingView
        className="flex-1"
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ProgressBar
          currentStep={currentStep}
          totalSteps={4}
          stepTitles={STEP_TITLES}
        />

        <View className="flex-1">{renderCurrentStep()}</View>

        <View className="flex-row justify-between px-5 py-4 bg-card border-t border-border">
          <Button
            variant="outline"
            onPress={previousStep}
            disabled={currentStep === 1}
            className="flex-row items-center gap-2"
          >
            <ArrowLeft size={20} color={colors.textSecondary} />
            <Text className="text-muted-foreground">Previous</Text>
          </Button>

          <Button
            onPress={nextStep}
            disabled={isSubmitting}
            className="flex-row items-center gap-2"
          >
            {isSubmitting ? (
              <LoadingSpinner
                size={20}
                isVisible={true}
                color={colors.textInverse}
              />
            ) : currentStep === 4 ? (
              <CheckCircle size={20} color={colors.textInverse} />
            ) : (
              <ArrowRight size={20} color={colors.textInverse} />
            )}
            <Text className="text-primary-foreground">
              {isSubmitting
                ? 'Submitting...'
                : currentStep === 4
                  ? 'Submit'
                  : 'Next'}
            </Text>
          </Button>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
