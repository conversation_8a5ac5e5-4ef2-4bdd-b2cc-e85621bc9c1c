import React, { useState } from 'react';
import { View, TextInput, TouchableOpacity } from 'react-native';
import { Eye, EyeOff, CircleAlert as AlertCircle } from 'lucide-react-native';
import { useThemeColors } from '@/lib/store/selectors';
import { Text } from '@/components/ui/text';
import { cn } from '@/lib/utils';

interface FormFieldProps {
  readonly label: string;
  readonly value: string;
  readonly onChangeText: (text: string) => void;
  readonly placeholder?: string;
  readonly error?: string;
  readonly required?: boolean;
  readonly secureTextEntry?: boolean;
  readonly keyboardType?: 'default' | 'email-address' | 'numeric' | 'phone-pad';
  readonly autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters';
  readonly multiline?: boolean;
  readonly numberOfLines?: number;
  readonly accessibilityLabel?: string;
  readonly accessibilityHint?: string;
}

export default function FormField({
  label,
  value,
  onChangeText,
  placeholder,
  error,
  required = false,
  secureTextEntry = false,
  keyboardType = 'default',
  autoCapitalize = 'sentences',
  multiline = false,
  numberOfLines = 1,
  accessibilityLabel,
  accessibilityHint,
}: FormFieldProps) {
  const colors = useThemeColors();
  const [isSecureTextVisible, setIsSecureTextVisible] = useState(false);

  const toggleSecureText = () => {
    setIsSecureTextVisible(!isSecureTextVisible);
  };

  return (
    <View className="mb-5">
      <Text className="text-base font-medium text-foreground mb-2">
        {label}
        {required && (
          <Text className="text-base font-medium text-destructive"> *</Text>
        )}
      </Text>

      <View
        className={cn(
          'flex-row items-center border rounded-lg px-3 min-h-12',
          error ? 'border-destructive' : 'border-border',
          'bg-card'
        )}
      >
        <TextInput
          className={cn(
            'flex-1 text-base text-foreground py-3',
            multiline && 'text-top'
          )}
          style={{
            minHeight: multiline ? numberOfLines * 20 + 20 : undefined,
          }}
          value={value}
          onChangeText={onChangeText}
          placeholder={placeholder}
          placeholderTextColor={colors.textSecondary}
          secureTextEntry={secureTextEntry && !isSecureTextVisible}
          keyboardType={keyboardType}
          autoCapitalize={autoCapitalize}
          multiline={multiline}
          numberOfLines={numberOfLines}
          accessibilityLabel={accessibilityLabel ?? label}
          accessibilityHint={accessibilityHint}
          accessibilityRole="text"
        />

        {secureTextEntry && (
          <TouchableOpacity
            className="p-1"
            onPress={toggleSecureText}
            accessibilityLabel={
              isSecureTextVisible ? 'Hide password' : 'Show password'
            }
          >
            {isSecureTextVisible ? (
              <EyeOff size={20} color={colors.textSecondary} />
            ) : (
              <Eye size={20} color={colors.textSecondary} />
            )}
          </TouchableOpacity>
        )}
      </View>

      {error && (
        <View className="flex-row items-center mt-2">
          <AlertCircle size={16} color={colors.error} />
          <Text className="text-sm text-destructive ml-1.5 flex-1">
            {error}
          </Text>
        </View>
      )}
    </View>
  );
}
