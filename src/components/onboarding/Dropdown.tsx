import React, { useState } from 'react';
import { View, TouchableOpacity, Modal, ScrollView } from 'react-native';
import {
  ChevronDown,
  Check,
  CircleAlert as AlertCircle,
} from 'lucide-react-native';
import { useThemeColors } from '@/lib/store/selectors';
import { Text } from '@/components/ui/text';
import { cn } from '@/lib/utils';

interface DropdownOption {
  value: string;
  label: string;
}

interface DropdownProps {
  readonly label: string;
  readonly value: string;
  readonly options: DropdownOption[];
  readonly onSelect: (value: string) => void;
  readonly placeholder?: string;
  readonly error?: string;
  readonly required?: boolean;
  readonly accessibilityLabel?: string;
}

export default function Dropdown({
  label,
  value,
  options,
  onSelect,
  placeholder = 'Select an option',
  error,
  required = false,
  accessibilityLabel,
}: DropdownProps) {
  const colors = useThemeColors();
  const [isOpen, setIsOpen] = useState(false);

  const selectedOption = options.find(option => option.value === value);

  const handleSelect = (optionValue: string) => {
    onSelect(optionValue);
    setIsOpen(false);
  };

  return (
    <View className="mb-5">
      <Text className="text-base font-medium text-foreground mb-2">
        {label}
        {required && (
          <Text className="text-base font-medium text-destructive"> *</Text>
        )}
      </Text>

      <TouchableOpacity
        className={cn(
          'flex-row items-center justify-between border rounded-lg px-3 py-3.5 min-h-12',
          error ? 'border-destructive' : 'border-border',
          'bg-card'
        )}
        onPress={() => setIsOpen(true)}
        accessibilityLabel={accessibilityLabel ?? `${label} dropdown`}
        accessibilityHint="Tap to select an option"
        accessibilityRole="button"
      >
        <Text
          className={cn(
            'text-base flex-1',
            selectedOption ? 'text-foreground' : 'text-muted-foreground'
          )}
        >
          {selectedOption ? selectedOption.label : placeholder}
        </Text>
        <ChevronDown size={20} color={colors.textSecondary} />
      </TouchableOpacity>

      {error && (
        <View className="flex-row items-center mt-2">
          <AlertCircle size={16} color={colors.error} />
          <Text className="text-sm text-destructive ml-1.5 flex-1">
            {error}
          </Text>
        </View>
      )}

      <Modal
        visible={isOpen}
        transparent
        animationType="slide"
        onRequestClose={() => setIsOpen(false)}
      >
        <View className="flex-1 bg-black/50 justify-center items-center">
          <View className="w-[90%] max-w-[400px] bg-card rounded-xl p-5 max-h-[80%]">
            <View className="flex-row justify-between items-center mb-5">
              <Text className="text-lg font-semibold text-foreground">
                Select {label}
              </Text>
              <TouchableOpacity
                onPress={() => setIsOpen(false)}
                className="p-2"
              >
                <Text className="text-base font-medium text-primary">
                  Cancel
                </Text>
              </TouchableOpacity>
            </View>

            <ScrollView className="max-h-[300px]">
              {options.map(option => (
                <TouchableOpacity
                  key={option.value}
                  className="flex-row items-center justify-between py-4 border-b border-border"
                  onPress={() => handleSelect(option.value)}
                  accessibilityLabel={option.label}
                  accessibilityRole="button"
                >
                  <Text className="text-base text-foreground flex-1">
                    {option.label}
                  </Text>
                  {value === option.value && (
                    <Check size={20} color={colors.primary} />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </View>
      </Modal>
    </View>
  );
}
