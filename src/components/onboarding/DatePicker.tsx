import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Platform,
  StyleSheet,
  Modal,
} from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Calendar, CircleAlert as AlertCircle } from 'lucide-react-native';
import { format } from 'date-fns';
import { useTheme } from '@/lib/hooks/useTheme';
import { cn } from '@/lib/utils';

interface DatePickerProps {
  readonly label: string;
  readonly value: Date | null | undefined;
  readonly onChange: (date: Date) => void;
  readonly error?: string;
  readonly required?: boolean;
  readonly minimumDate?: Date;
  readonly maximumDate?: Date;
  readonly accessibilityLabel?: string;
}

export default function DatePicker({
  label,
  value,
  onChange,
  error,
  required = false,
  minimumDate,
  maximumDate,
  accessibilityLabel,
}: DatePickerProps) {
  const { isDark, colors } = useTheme();
  const [showPicker, setShowPicker] = useState(false);

  const handleDateChange = (event: any, selectedDate?: Date) => {
    if (Platform.OS === 'android') {
      setShowPicker(false);
    }

    if (selectedDate) {
      onChange(selectedDate);
    }
  };

  const formatDisplayDate = (date: Date | null | undefined) => {
    if (!date) return 'Select date';
    return format(new Date(date), 'MMM dd, yyyy');
  };

  const showDatePicker = () => {
    setShowPicker(true);
  };

  const hideDatePicker = () => {
    setShowPicker(false);
  };

  return (
    <View className="mb-5">
      <Text className="text-base font-medium text-foreground mb-2">
        {label}
        {required && (
          <Text className="text-base font-medium text-destructive"> *</Text>
        )}
      </Text>

      <TouchableOpacity
        className={cn(
          'flex-row items-center justify-between border rounded-lg px-3 py-3.5 min-h-[48px] bg-background',
          error ? 'border-destructive' : 'border-border'
        )}
        onPress={showDatePicker}
        accessibilityLabel={accessibilityLabel ?? `${label} date picker`}
        accessibilityHint="Tap to select a date"
        accessibilityRole="button"
      >
        <Text
          className={cn(
            'text-base',
            value ? 'text-foreground' : 'text-muted-foreground'
          )}
        >
          {formatDisplayDate(value)}
        </Text>
        <Calendar size={20} className="text-muted-foreground" />
      </TouchableOpacity>

      {error && (
        <View className="flex-row items-center mt-2">
          <AlertCircle size={16} className="text-destructive" />
          <Text className="text-sm text-destructive ml-1.5 flex-1">
            {error}
          </Text>
        </View>
      )}

      {showPicker && (
        <>
          {Platform.OS === 'ios' && (
            <View className="absolute inset-0 bg-black/50 justify-end z-50">
              <View className="bg-background px-4 py-3 items-end">
                <TouchableOpacity onPress={hideDatePicker}>
                  <Text className="text-base font-medium text-primary">
                    Done
                  </Text>
                </TouchableOpacity>
              </View>
              <DateTimePicker
                value={value || new Date()}
                mode="date"
                display="spinner"
                onChange={handleDateChange}
                minimumDate={minimumDate}
                maximumDate={maximumDate}
                themeVariant={isDark ? 'dark' : 'light'}
              />
            </View>
          )}

          {Platform.OS === 'android' && (
            <DateTimePicker
              value={value || new Date()}
              mode="date"
              display="default"
              onChange={handleDateChange}
              minimumDate={minimumDate}
              maximumDate={maximumDate}
            />
          )}

          <Modal
            transparent
            animationType="slide"
            visible={showPicker}
            onRequestClose={() => setShowPicker(false)}
          >
            <View style={styles.modalOverlay}>
              <View
                style={[
                  styles.modalContent,
                  { backgroundColor: colors.surface },
                ]}
              >
                <View style={styles.modalHeader}>
                  <Text style={[styles.modalTitle, { color: colors.text }]}>
                    Select {label}
                  </Text>
                  <TouchableOpacity
                    onPress={() => setShowPicker(false)}
                    style={styles.closeButton}
                  >
                    <Text
                      style={[
                        styles.closeButtonText,
                        { color: colors.primary },
                      ]}
                    >
                      Cancel
                    </Text>
                  </TouchableOpacity>
                </View>

                <WebDatePicker
                  value={value}
                  onChange={onChange}
                  minimumDate={minimumDate}
                  maximumDate={maximumDate}
                />
              </View>
            </View>
          </Modal>
        </>
      )}
    </View>
  );
}

// Web-specific date picker component
function WebDatePicker({
  value,
  onChange,
  minimumDate,
  maximumDate,
}: Readonly<{
  value: Date | null | undefined;
  onChange: (date: Date) => void;
  minimumDate?: Date;
  maximumDate?: Date;
}>) {
  const { colors } = useTheme();
  const handleDateChange = (dateString: string) => {
    const date = new Date(dateString);
    if (!isNaN(date.getTime())) {
      onChange(date);
    }
  };

  return (
    <View style={styles.webDatePicker}>
      <input
        type="date"
        value={value ? format(new Date(value), 'yyyy-MM-dd') : ''}
        onChange={e => handleDateChange(e.target.value)}
        min={minimumDate ? format(minimumDate, 'yyyy-MM-dd') : undefined}
        max={maximumDate ? format(maximumDate, 'yyyy-MM-dd') : undefined}
        style={{
          width: '100%',
          padding: 12,
          fontSize: 16,
          borderRadius: 8,
          border: `1px solid ${colors.border}`,
          backgroundColor: colors.surface,
          color: colors.text,
        }}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
  },
  label: {
    fontFamily: 'Inter-Medium',
    fontSize: 16,
    marginBottom: 8,
  },
  required: {
    fontFamily: 'Inter-Medium',
    fontSize: 16,
  },
  dateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 14,
    minHeight: 48,
  },
  dateText: {
    fontFamily: 'Inter-Regular',
    fontSize: 16,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  errorText: {
    fontFamily: 'Inter-Regular',
    fontSize: 14,
    marginLeft: 6,
    flex: 1,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxWidth: 400,
    borderRadius: 12,
    padding: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 18,
  },
  closeButton: {
    padding: 8,
  },
  closeButtonText: {
    fontFamily: 'Inter-Medium',
    fontSize: 16,
  },
  webDatePicker: {
    marginBottom: 20,
  },
  nativeDatePicker: {
    padding: 20,
    alignItems: 'center',
  },
});
